#include "my_scheduler.h"

typedef struct{
	void(*task_fun)(void);
	uint32_t task_time;
	uint32_t last_time;
}task;

task all_task[]={
	//{led_task,1,0},
	{Gray_Task,5,0},
	//{Encoder_Task,5,0},
	//{PID_Task,5,0},
	{bno080_task,10,0},
	{uart_task,5,0},
	{key_task,10,0},
	{oled_task,100,0}
};

uint8_t task_num;

void all_task_init(void)
{
	extern UART_HandleTypeDef huart1;

	task_num = sizeof(all_task)/sizeof(task);

	HAL_UART_Transmit(&huart1, (uint8_t*)"Init: OLED\r\n", 12, 1000);
	my_oled_init();

	HAL_UART_Transmit(&huart1, (uint8_t*)"Init: UART\r\n", 12, 1000);
	uart_init();

	HAL_UART_Transmit(&huart1, (uint8_t*)"Init: BNO080\r\n", 14, 1000);
	my_bno080_init();

	HAL_UART_Transmit(&huart1, (uint8_t*)"Init: LED\r\n", 11, 1000);
	led_init();

	HAL_UART_Transmit(&huart1, (uint8_t*)"Init: PID\r\n", 11, 1000);
	PID_Init();

	HAL_UART_Transmit(&huart1, (uint8_t*)"Init: Encoder\r\n", 15, 1000);
	Encoder_Init();

	HAL_UART_Transmit(&huart1, (uint8_t*)"Init: Motor\r\n", 13, 1000);
	Motor_Init();

//	jy901s_init();
	HAL_UART_Transmit(&huart1, (uint8_t*)"Init: Gray\r\n", 12, 1000);
	Gray_Init();

	HAL_UART_Transmit(&huart1, (uint8_t*)"Init: Timer\r\n", 13, 1000);
	timer_init();

	HAL_UART_Transmit(&huart1, (uint8_t*)"All init complete\r\n", 19, 1000);
}

void all_task_run(void)
{
	for(uint8_t i=0;i<task_num;i++)
	{
		uint32_t now_time = HAL_GetTick();
		if(now_time >= all_task[i].last_time + all_task[i].task_time)
		{
			all_task[i].last_time = now_time;
			all_task[i].task_fun();
		}
	}	
}
