#include "usart_app.h"
#include "jy901s_driver.h"

extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;

extern UART_HandleTypeDef huart5;

uint8_t uart_rx_dma_buffer[128] = {0};
uint8_t uart_dma_buffer[128] = {0};
struct rt_ringbuffer uart_ringbuffer;
uint8_t ringbuffer_pool[128];

int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512];
	va_list arg;
	int len;
	// ��ʼ���ɱ�����б�
	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}

void uart_init(void)
{
	/* ���ڻ��λ�������ʼ���������첽������������ */
  rt_ringbuffer_init(&uart_ringbuffer, ringbuffer_pool, sizeof(ringbuffer_pool));
	/*��������DMA�����ж�*/
	HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
	__HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
}
	
/**
 * @brief UART DMA������ɻ�����¼��ص�����
 * @param huart UART���
 * @param Size ָʾ���¼�����ǰ��DMA�Ѿ��ɹ������˶����ֽڵ�����
 * @retval None
 */
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
	// 1. ȷ����Ŀ�괮�� (USART1)
	if (huart->Instance == USART1)
	{
		// 2. ����ֹͣ��ǰ�� DMA ���� (������ڽ�����)
		//    ��Ϊ�����ж���ζ�ŷ��ͷ��Ѿ�ֹͣ����ֹ DMA �����ȴ������
		HAL_UART_DMAStop(huart);

		rt_ringbuffer_put(&uart_ringbuffer, uart_rx_dma_buffer, Size);

		// 5. ��� DMA ���ջ�������Ϊ�´ν�����׼��
		//    ��Ȼ memcpy ֻ������ Size �������������������������
		memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));

		// 6. **�ؼ�������������һ�� DMA ���н���**
		//    �����ٴε��ã�����ֻ�������һ��
		HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));

		// 7. ���֮ǰ�ر��˰����жϣ�������Ҫ�������ٴιر� (������Ҫ)
		__HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
	}
}


void uart_task(void)
{
	uint16_t length;

	length = rt_ringbuffer_data_len(&uart_ringbuffer);

//	my_printf(&huart1, "666\r\n");
	
	if (length == 0)
		return;

	rt_ringbuffer_get(&uart_ringbuffer, uart_dma_buffer, length);
	
	uint8_t num = 2;
	int8_t pwm = 2;
	sscanf(uart_dma_buffer, "set_%d:%d", &num, &pwm);
	if(num == 0)
	{
//		motor_set_l(pwm);
		pid_set_target(&pid_speed_left, pwm);
		my_printf(&huart1, "set_l_ok:%d\r\n", pwm);
	}
	else if(num == 1)
	{
		motor_set_r(pwm);
		pid_set_target(&pid_speed_right, pwm);
		my_printf(&huart1, "set_r_ok:%d\r\n", pwm);
	}
	else
	{
		my_printf(&huart1, "uart1:%s\r\n", uart_dma_buffer);
		my_printf(&huart5, "uart1:%s\r\n", uart_dma_buffer);
	}
		
	
	// ��ս��ջ�����
	memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));
}
