[Version]
Nu_LinkVersion=V5.15
[Process]
ProcessID=0x00000000
ProcessCreationTime_L=0x00000000
ProcessCreationTime_H=0x00000000
NuLinkID=0x00000000
[ChipSelect]
;ChipName=<NUC1xx|NUC2xx|M05x|N571|N572|Nano100|N512|Mini51|NUC505|General>
ChipName=NUC1xx
[NUC505]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=NUC505_SPIFLASH.FLM
[NUC4xx]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=NUC400_AP_512.FLM
TraceConf0=0x00000002
TraceConf1=0x014fb180
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[NUC2xx]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NUC200_AP_128.FLM
[NUC1311]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NUC1311_AP_64.FLM
[NUC126]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=NUC126_AP_256.FLM
[NUC121]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NUC121_AP_32.FLM
[NUC1xx]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NUC100_AP_128.FLM
[NUC029]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=NUC029_AP_16.FLM
[NM1820]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=NM1820_AP_17_5.FLM
[NM1810]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=NM1810_AP_29_5.FLM
[NM1500]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NM1500_AP_128.FLM
[NM1330]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NM1330_AP_64.FLM
[NM1320]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NM1320_AP_32.FLM
[NM1240]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NM1240_AP_64.FLM
[NM1230]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NM1230_AP_64.FLM
[NM1200]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=NM1200_AP_8.FLM
[NM1120]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=NM1120_AP_29_5.FLM
[TF5100]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=TF5100_AP_64.FLM
[NDA102]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=NDA102_AP_29_5.FLM
[Nano103]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=Nano103_AP_64.FLM
[Nano100]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=Nano100_AP_64.FLM
[N576]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=N576_AP_145.FLM
[N575]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=N575_AP_145.FLM
[N572]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=N572Fxxx.FLM
[N571]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=N571E000.FLM
[N570]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=N570_AP_64.FLM
[N569]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=N569_AP_64.FLM
[N512]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=N512_AP_64.FLM
[Mini57]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=Mini57_AP_29_5.FLM
[Mini51]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=Mini51_AP_16.FLM
[M481]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M481_AP_512.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[M480LD]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M480LD_AP_256.FLM
[M479]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M479_AP_256.FLM
[M451]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M451_AP_256.FLM
[M471]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Bank=0
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M471_AP_512.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[M251]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=M251_AP_192.FLM
[M2351]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M2351_AP_512.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[M261]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M261_AP_512.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[MR63]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=MR63_AP_512.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[M2354]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Bank=0
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
CheckDPM=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M2354_AP_1M.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[M071]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=M071_AP_128.FLM
[M0564]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=M0564_AP_256.FLM
[M0519]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=M0519_AP_128.FLM
[M0518]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=M0518_AP_64.FLM
[M05x]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=M0516_AP_64.FLM
[M0A21]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=M0A21_AP_32.FLM
[M030G]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
DisableTimeoutDetect=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=M030G_AP_64.FLM
[M031]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Bank=0
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=M031_AP_128.FLM
[NPCX]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=NPCX_AP_512.FLM
[I96000]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
Erase=2
Program=0
Verify=0
ResetAndRun=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x8000
ProgramAlgorithm=
[I94000]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=I94000_AP_512.FLM
[ISD9300]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=ISD9300_AP_145.FLM
[I9200]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=I9200_AP_128.FLM
[ISD9xxx]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=ISD9100_AP_145.FLM
[ISD9000]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=ISD9000_AP_64.FLM
[AU9xxx]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=AU9100_AP_145.FLM
[Autodetect]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=
[General]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=