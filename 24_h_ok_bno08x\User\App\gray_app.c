#include "gray_app.h"
#include "software_iic.h" // 直接使用软件I2C

extern UART_HandleTypeDef huart1;

unsigned char Digtal; 

float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f}; 

float g_line_position_error; 

void Gray_Init(void)
{
    // 测试软件I2C连接
    if(Ping() == 0) {
        my_printf(&huart1, "Gray Sensor Connected Successfully!\r\n");
    } else {
        my_printf(&huart1, "Gray Sensor Connection Failed!\r\n");
    }
}

void Gray_Task(void)
{
    static uint32_t last_print_time = 0;
    static uint8_t last_digital = 0xFF; // 记录上次的数字状态

    HAL_NVIC_DisableIRQ(TIM2_IRQn);
    uint8_t temp = 0;
    temp = IIC_Get_Digtal();

    if(temp == 0xAA)
    {
        // I2C读取失败，打印错误信息
        if(HAL_GetTick() - last_print_time > 1000) { // 每秒最多打印一次错误
            my_printf(&huart1, "[ERROR] Gray sensor I2C read failed!\r\n");
            last_print_time = HAL_GetTick();
        }
        HAL_NVIC_EnableIRQ(TIM2_IRQn);
        return;
    }

    Digtal = ~temp; // 取反得到实际的传感器状态
    HAL_NVIC_EnableIRQ(TIM2_IRQn);

    // 计算加权位置和黑线数量
    float weighted_sum = 0;
    uint8_t black_line_count = 0;

    for(uint8_t i = 0; i < 8; i++)
    {
        if((Digtal >> i) & 0x01)
        {
            weighted_sum += gray_weights[i];
            black_line_count++;
        }
    }

    if(black_line_count > 0)
        g_line_position_error = weighted_sum / (float)black_line_count;
    else
        g_line_position_error = 0.0f;

    // 串口打印逻辑：状态改变时立即打印，或每500ms打印一次
    uint32_t current_time = HAL_GetTick();
    bool state_changed = (Digtal != last_digital);
    bool time_to_print = (current_time - last_print_time > 500);

    if(state_changed || time_to_print)
    {
        // 打印详细的灰度传感器状态
        my_printf(&huart1, "=== Gray Sensor Status ===\r\n");

        // 打印原始数据
        my_printf(&huart1, "Raw: 0x%02X -> Digital: %d%d%d%d%d%d%d%d\r\n",
                  temp,
                  (Digtal>>0)&0x01, (Digtal>>1)&0x01, (Digtal>>2)&0x01, (Digtal>>3)&0x01,
                  (Digtal>>4)&0x01, (Digtal>>5)&0x01, (Digtal>>6)&0x01, (Digtal>>7)&0x01);

        // 打印传感器状态描述
        my_printf(&huart1, "Sensors: ");
        for(int i = 0; i < 8; i++)
        {
            if((Digtal >> i) & 0x01)
                my_printf(&huart1, "[B]"); // Black - 检测到黑线
            else
                my_printf(&huart1, "[W]"); // White - 检测到白色
        }
        my_printf(&huart1, "\r\n");

        // 打印黑线数量和位置误差
        my_printf(&huart1, "Black lines: %d, Position error: %.2f\r\n",
                  black_line_count, g_line_position_error);

        // 打印线条位置判断
        if(black_line_count == 0)
        {
            my_printf(&huart1, "Status: No line detected\r\n");
        }
        else if(black_line_count >= 6)
        {
            my_printf(&huart1, "Status: Wide line or intersection\r\n");
        }
        else
        {
            if(g_line_position_error < -2.0f)
                my_printf(&huart1, "Status: Line on LEFT side\r\n");
            else if(g_line_position_error > 2.0f)
                my_printf(&huart1, "Status: Line on RIGHT side\r\n");
            else
                my_printf(&huart1, "Status: Line in CENTER\r\n");
        }

        my_printf(&huart1, "========================\r\n\r\n");

        last_print_time = current_time;
        last_digital = Digtal;
    }
}
