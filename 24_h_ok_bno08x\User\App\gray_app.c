#include "gray_app.h"
#include "software_iic.h" // 直接使用软件I2C

extern UART_HandleTypeDef huart1;

unsigned char Digtal; 

float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f}; 

float g_line_position_error; 

void Gray_Init(void)
{
    // 测试软件I2C连接
    if(Ping() == 0) {
        my_printf(&huart1, "Gray Sensor Connected Successfully!\r\n");
    } else {
        my_printf(&huart1, "Gray Sensor Connection Failed!\r\n");
    }
}

void Gray_Task(void)
{
		// 最简单的调试：直接使用HAL_UART_Transmit
		HAL_UART_Transmit(&huart1, (uint8_t*)"Gray_Task START\r\n", 17, 1000);

		// 暂时跳过I2C读取，直接使用模拟数据进行测试
		static uint8_t test_counter = 0;
		test_counter++;

		// 模拟不同的传感器状态
		uint8_t temp;
		switch(test_counter % 4) {
			case 0: temp = 0x00; break; // 全白
			case 1: temp = 0xFF; break; // 全黑
			case 2: temp = 0x0F; break; // 左半边黑
			case 3: temp = 0xF0; break; // 右半边黑
		}

		Digtal = temp; // 直接赋值，不取反

		char debug_msg[100];
		sprintf(debug_msg, "Test data: 0x%02X -> %d%d%d%d%d%d%d%d\r\n",
			temp,
			(Digtal>>0)&0x01, (Digtal>>1)&0x01, (Digtal>>2)&0x01, (Digtal>>3)&0x01,
			(Digtal>>4)&0x01, (Digtal>>5)&0x01, (Digtal>>6)&0x01, (Digtal>>7)&0x01);
		HAL_UART_Transmit(&huart1, (uint8_t*)debug_msg, strlen(debug_msg), 1000);

		// 计算加权位置
		float weighted_sum = 0;
		uint8_t black_line_count = 0;

		for(uint8_t i = 0; i < 8; i++)
		{
			if((Digtal>>i) & 0x01)
			{
				weighted_sum += gray_weights[i];
				black_line_count++;
			}
		}

		if(black_line_count > 0)
			g_line_position_error = weighted_sum / (float)black_line_count;

		HAL_UART_Transmit(&huart1, (uint8_t*)"Gray_Task END\r\n", 15, 1000);
}
